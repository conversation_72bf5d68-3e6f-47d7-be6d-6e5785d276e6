function [ T ] = Rosenbrock_( X , M,opt )
%ROSENBROCK20 Summary of this function goes here
% %%%%%Shifte<PERSON><PERSON>’s Function [-100 100] F6
T = [];

for t = 1:size(X,1)
    x = X(t,:);
    var = x;
    dim = length(var);
    var = (M*(var-opt)')';
    sum = 0;
    for ii = 1:(dim-1)
        xi = var(ii);
        xnext = var(ii+1);
        new = 100*(xnext-xi^2)^2 + (xi-1)^2;
        sum = sum + new;
    end
    obj = sum;

    T(t,:) = obj;

end
end

